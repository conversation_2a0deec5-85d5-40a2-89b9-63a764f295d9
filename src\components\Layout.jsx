import React from 'react';
import { Link, useLocation } from 'react-router-dom';

const Layout = ({ children }) => {
  const location = useLocation();
  const isHomePage = location.pathname === '/';

  return (
    <div dir="rtl" className="min-h-screen bg-gray-50 text-gray-900 font-sans">
      <header className="bg-white shadow-md">
        <div className="max-w-6xl mx-auto px-6 py-6 flex items-center justify-between">
          <div>
            <Link to="/" className="block">
              <h1 className="text-2xl font-extrabold hover:text-indigo-600 transition-colors">
                صحابة الإمام علي عليه السلام
              </h1>
              <p className="text-sm text-gray-500">موسوعة تعليمية حديثة — مُنسقة باللغة العربية (RTL)</p>
            </Link>
          </div>
          <nav className="space-x-4 flex items-center">
            {!isHomePage && (
              <Link
                to="/"
                className="px-4 py-2 bg-indigo-600 text-white rounded-lg shadow-sm hover:bg-indigo-700 transition-colors"
              >
                العودة للرئيسية
              </Link>
            )}
            <button
              onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg shadow-sm hover:bg-gray-700 transition-colors"
            >
              أعلى الصفحة
            </button>
            <button
              onClick={() => window.print()}
              className="px-4 py-2 border rounded-lg hover:bg-gray-50 transition-colors"
            >
              طباعة/حفظ
            </button>
          </nav>
        </div>
      </header>

      <main className="max-w-6xl mx-auto p-6">
        {children}
      </main>

      <footer className="max-w-6xl mx-auto p-6 mt-10 text-gray-500 text-sm">
        <p>حقوق المحتوى محفوظة — موقع تعليمي مُصمّم للمراجعة والدراسة.</p>
      </footer>
    </div>
  );
};

export default Layout;
