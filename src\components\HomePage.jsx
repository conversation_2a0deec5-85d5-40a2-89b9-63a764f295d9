import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { companionsData } from '../data/companionsData';

const HomePage = () => {
  const [query, setQuery] = useState("");

  const filtered = companionsData.filter((c) =>
    `${c.name} ${c.short}`.includes(query) || c.name.includes(query)
  );

  const handleShare = (companion) => {
    const url = `${window.location.origin}/companion/${companion.id}`;
    if (navigator.share) {
      navigator.share({
        title: companion.name,
        text: companion.short,
        url: url,
      });
    } else if (navigator.clipboard) {
      navigator.clipboard.writeText(url);
      alert('تم نسخ الرابط إلى الحافظة');
    }
  };

  return (
    <>
      <section className="bg-white p-6 rounded-2xl shadow-sm mb-6">
        <h2 className="text-xl font-bold mb-2">مقدمة</h2>
        <p className="text-gray-700 leading-relaxed">
          هذا الموقع يقدّم نظرة تعليمية ومنهجية على أبرز أصحاب الإمام علي عليه السلام — من القادة العسكريين إلى
          الزهاد والرواة. يمكنك البحث عن أي صحابي لقراءة تعريف تفصيلي عنه، والاطّلاع على مآثره ودوره التاريخي.
          كل صحابي له صفحة مخصصة قابلة للمشاركة والحفظ.
        </p>
      </section>

      <section className="flex items-center gap-4 mb-6">
        <input
          placeholder="ابحث باسم الصحابي أو بكلمة..."
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="flex-1 p-3 rounded-xl border focus:outline-none focus:ring-2 focus:ring-indigo-500"
        />
        <div className="text-sm text-gray-500">وجد: <strong>{filtered.length}</strong></div>
      </section>

      <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filtered.map((c) => (
          <article key={c.id} className="bg-white p-4 rounded-2xl shadow hover:shadow-md transition">
            <h3 className="text-lg font-bold">{c.name}</h3>
            <p className="text-sm text-gray-500">{c.years}</p>
            <p className="mt-3 text-gray-700">{c.short}</p>
            <div className="mt-4 flex gap-3 flex-wrap">
              <Link
                to={`/companion/${c.id}`}
                className="px-3 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
              >
                قراءة التفاصيل
              </Link>
              <button
                onClick={() => handleShare(c)}
                className="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                مشاركة
              </button>
              <button
                onClick={() => navigator.clipboard && navigator.clipboard.writeText(`${c.name} - ${c.short}`)}
                className="px-3 py-2 border rounded-lg hover:bg-gray-50 transition-colors"
              >
                نسخ الملخّص
              </button>
            </div>
          </article>
        ))}
      </section>

      {filtered.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">لم يتم العثور على نتائج للبحث "{query}"</p>
          <button
            onClick={() => setQuery("")}
            className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
          >
            إعادة تعيين البحث
          </button>
        </div>
      )}
    </>
  );
};

export default HomePage;
