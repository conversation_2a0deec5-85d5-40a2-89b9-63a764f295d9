import React from 'react';
import { Link } from 'react-router-dom';

const NotFound = () => {
  return (
    <div className="text-center py-16">
      <div className="mb-8">
        <h1 className="text-6xl font-bold text-gray-300 mb-4">404</h1>
        <h2 className="text-2xl font-bold text-gray-800 mb-4">الصفحة غير موجودة</h2>
        <p className="text-gray-600 mb-8 max-w-md mx-auto">
          عذراً، الصفحة التي تبحث عنها غير موجودة. ربما تم حذفها أو تغيير رابطها.
        </p>
      </div>
      
      <div className="space-y-4">
        <Link
          to="/"
          className="inline-block px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
        >
          العودة للصفحة الرئيسية
        </Link>
        
        <div className="text-sm text-gray-500">
          أو يمكنك تصفح جميع الصحابة من الصفحة الرئيسية
        </div>
      </div>
    </div>
  );
};

export default NotFound;
