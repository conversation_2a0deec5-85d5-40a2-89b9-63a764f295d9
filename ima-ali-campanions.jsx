import React, { useState } from "react";

// موقع تعليمي عصري عن صحابة الإمام علي عليه السلام
// مكوّن React واحد (قابل للعرض مباشرة في بيئة مثل CodeSandbox أو Replit).
// يستخدم TailwindCSS للكلاسات. تخطيط RTL جاهز.

export default function CompanionsApp() {
  const companionsData = [
    {
      id: 1,
      name: "مالك الأشتر النخعي",
      years: "توفي 38 هـ",
      short: "قائد عسكري ووالي مُنَصَب من الإمام علي، من أبرز أنصاره في السلوك العسكري والسياسي.",
      details:
        `مالك الأشتر النخعي من قادة جيش الإمام علي وولاة مهمين في عهده. اشتهر بشجاعته، ووفائه، وحسن إدارته. عيّنه الإمام واليًا على مصر وأرسل له رسالة إدارية معروفة (رسالة مالك الأشتر) تتضمن مبادئ القضاء والإدارة والعدالة.`,
      sources: ["نهج البلاغة - رسائل الإمام علي", "الاستيعاب في معرفة الأصحاب"]
    },
    {
      id: 2,
      name: "عمار بن ياسر",
      years: "توفي 37 هـ",
      short: "من أوائل المؤمنين، تعرّض للتعذيب في مكة، ومات شهيدًا في صفين دفاعًا عن علي.",
      details:
        `عمار بن ياسر صحابي تابع للإسلام منذ بداياته، وقد ذُكر في النصوص الإسلامية لتضحياته وصموده. كان من الداعمين الأقوياء للإمام علي، وشارك في موقعة صفين حيث استشهد وهو يدافع عن الحق الذي آمن به.`,
      sources: ["صحيح مسلم", "الإصابة في تمييز الصحابة"]
    },
    {
      id: 3,
      name: "حجر بن عدي الكندي",
      years: "توفي 51 هـ",
      short: "تابعي جليل من أنصار علي، اعتُبر من رموز الصمود، قُتله معاوية  بعد رفضه لعن الإمام.",
      details:
        `حجر بن عدي من أبطال الحق الذين وقفوا مع الإمام علي في مواجهة الظلم. استُشهد دفاعًا عن مبادئه ورفضًا للتخاذل عن نصرة الإمام.`,
      sources: ["تاريخ الطبري", "البداية والنهاية"]
    },
    {
      id: 4,
      name: "الأصبغ بن نباتة التميمي",
      years: "تابع/تاريخ الوفاة غير مؤكد بدقة",
      short: "ملازم ومُوثِّق لكثير من خطب الإمام علي ونقله عن أقواله.",
      details:
        `الأصبغ من خواص أصحاب الإمام علي الذين حفظوا كثيرًا من خطبه وكلماته. كان مقرّبًا للإمام ومشاركًا في حمل رسالته الفكرية والروحية.`,
      sources: ["نهج البلاغة", "رجال الكشي"]
    },
    {
      id: 5,
      name: "كميل بن زياد النخعي",
      years: "توفي تقريبًا 82 هـ",
      short: "راوي دعاء كميل والزاهد في الدنيا، واستُشهد لاحقًا بسبب ولائه السياسي والروحي.",
      details:
        `كميل بن زياد من تلاميذ الإمام علي وقد روى عنه دعاءً مشهورًا يعرف بـ"دعاء كميل". اتهمه الخصوم بولائه لعلي فوقع ضحية لصراعات السلطة في مراحل لاحقة.`,
      sources: ["مصادر الأدعية والزيارات", "رجال النجاشي"]
    },
    {
      id: 6,
      name: "ميثم التمّار",
      years: "توفي 60 هـ",
      short: "زاهد وصاحب سرّ الإمام، تعرّض للتعذيب والقتل بسبب ولائه.",
      details:
        `ميثم التمّار كان من أقرب من اختار الإمام علي لمودته ومشاركته في أسرار دعوته. حُكم عليه بالموت على يد خصوم الإمام بعد أن رفض التنازل عن ولائه.`,
      sources: ["رجال الكشي", "مقاتل الطالبيين"]
    },
    {
      id: 7,
      name: "قيس بن سعد بن عبادة",
      years: "تاريخ الوفاة غير محدد بدقة",
      short: "قائد وسياسي من أنصار الإمام علي، تولّى مناصب إدارية وعسكرية.",
      details:
        `قيس بن سعد من أعلام الأنصار الذين ساندوا عليًا، وقد تقلّد بعض المسؤوليات القيادية خلال فترة الخلاف.`,
      sources: ["تاريخ اليعقوبي", "الطبقات الكبرى"]
    },
    {
      id: 8,
      name: "رشيد الهجري",
      years: "تاريخ الوفاة غير محدد بدقة",
      short: "مقرّب وحامل أسرار الإمام، استُشهد بسبب جهره بولائه لعلي.",
      details:
        `كان رشيد من أصحاب السرّ عند الإمام علي، عرف بولائه الثابت حتى نال الشهادة من خصوم الإمام.`,
      sources: ["رجال الكشي", "الاستيعاب في معرفة الأصحاب"]
    },
    {
      id: 9,
      name: "عبدالله بن عباس",
      years: "68 هـ",
      short: "ابن عم الرسول، من كبار علماء الصحابة والمفسرين، ناصر الإمام علي.",
      details:
        `عبدالله بن عباس عُرف بحبر الأمة وبحر العلم، وكان مقرّبًا من الإمام علي، وشارك في دعمه سياسيًا وعلميًا. ينقل عنه كثير من التفسير والحديث.`,
      sources: ["تفسير ابن عباس", "الإصابة في تمييز الصحابة"]
    },
    {
      id: 10,
      name: "سلمان الفارسي",
      years: "36 هـ تقريبًا",
      short: "من كبار الصحابة، رمز الإيمان والزهد، وكان من أوفى أنصار الإمام علي.",
      details:
        `سلمان الفارسي من أبرز الصحابة الذين آمنوا برسالة الإسلام منذ وقت مبكر. كان من أخلص أنصار الإمام علي ومثالاً للتقوى والزهد والوفاء.`,
      sources: ["حلية الأولياء", "مناقب آل أبي طالب"]
    }
  ];

  const [query, setQuery] = useState("");
  const [selected, setSelected] = useState(null);

  const filtered = companionsData.filter((c) =>
    `${c.name} ${c.short}`.includes(query) || c.name.includes(query)
  );

  return (
    <div dir="rtl" className="min-h-screen bg-gray-50 text-gray-900 font-sans">
      <header className="bg-white shadow-md">
        <div className="max-w-6xl mx-auto px-6 py-6 flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-extrabold">صحابة الإمام علي عليه السلام</h1>
            <p className="text-sm text-gray-500">موسوعة تعليمية حديثة — مُنسقة باللغة العربية (RTL)</p>
          </div>
          <nav className="space-x-4 flex items-center">
            <button
              onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg shadow-sm"
            >
              الصفحة الرئيسية
            </button>
            <button
              onClick={() => window.print()}
              className="px-4 py-2 border rounded-lg"
            >
              طباعة/حفظ
            </button>
          </nav>
        </div>
      </header>

      <main className="max-w-6xl mx-auto p-6">
        <section className="bg-white p-6 rounded-2xl shadow-sm mb-6">
          <h2 className="text-xl font-bold mb-2">مقدمة</h2>
          <p className="text-gray-700 leading-relaxed">
            هذا الموقع يقدّم نظرة تعليمية ومنهجية على أبرز أصحاب الإمام علي عليه السلام — من القادة العسكريين إلى
            الزهاد والرواة. يمكنك البحث عن أي صحابي لقراءة تعريف تفصيلي عنه، والاطّلاع على مآثره ودوره التاريخي.
          </p>
        </section>

        <section className="flex items-center gap-4 mb-6">
          <input
            placeholder="ابحث باسم الصحابي أو بكلمة..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="flex-1 p-3 rounded-xl border focus:outline-none"
          />
          <div className="text-sm text-gray-500">وجد: <strong>{filtered.length}</strong></div>
        </section>

        <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filtered.map((c) => (
            <article key={c.id} className="bg-white p-4 rounded-2xl shadow hover:shadow-md transition">
              <h3 className="text-lg font-bold">{c.name}</h3>
              <p className="text-sm text-gray-500">{c.years}</p>
              <p className="mt-3 text-gray-700">{c.short}</p>
              <div className="mt-4 flex gap-3">
                <button
                  onClick={() => setSelected(c)}
                  className="px-3 py-2 bg-indigo-600 text-white rounded-lg"
                >
                  قراءة التفاصيل
                </button>
                <button
                  onClick={() => navigator.clipboard && navigator.clipboard.writeText(`${c.name} - ${c.short}`)}
                  className="px-3 py-2 border rounded-lg"
                >
                  نسخ الملخّص
                </button>
              </div>
            </article>
          ))}
        </section>

        <footer className="mt-10 text-gray-500 text-sm">
          <p>حقوق المحتوى محفوظة — موقع تعليمي مُصمّم للمراجعة والدراسة.</p>
        </footer>
      </main>

      {/* Modal للتفاصيل */}
      {selected && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-6">
          <div className="bg-white rounded-2xl max-w-3xl w-full p-6 shadow-lg">
            <div className="flex justify-between items-start">
              <div>
                <h2 className="text-2xl font-bold">{selected.name}</h2>
                <p className="text-sm text-gray-500">{selected.years}</p>
              </div>
              <button onClick={() => setSelected(null)} className="text-gray-500">إغلاق ✕</button>
            </div>

            <div className="mt-4 text-gray-700 leading-relaxed">
              <p>{selected.details}</p>

              <h3 className="mt-4 font-semibold">المراجع والمصادر</h3>
              <ul className="list-disc mr-6 mt-2 text-gray-600">
                {selected.sources && selected.sources.map((s, i) => (
                  <li key={i}>{s}</li>
                ))}
              </ul>

              <h3 className="mt-4 font-semibold">ملاحظات تعليمية</h3>
              <ul className="list-disc mr-6 mt-2 text-gray-600">
                <li>راجع مصادر السيرة والكتب المتخصصة للتوسع في كل شخصية.</li>
                <li>قارن بين أدوار الصحابة في الجوانب العسكرية والسياسية والروحية.</li>
                <li>استخرج دروسًا أخلاقية وسلوكية من سيرة كل صحابي.</li>
              </ul>

              <div className="mt-6 flex gap-3">
                <button onClick={() => window.print()} className="px-4 py-2 bg-green-600 text-white rounded-lg">طباعة السيرة</button>
                <button
                  onClick={() => alert('Feature: Export to PDF (يمكن إضافتها لاحقًا)')}
                  className="px-4 py-2 border rounded-lg"
                >
                  تصدير PDF
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
