# صحابة الإمام علي عليه السلام

موقع تعليمي عصري عن أبرز أصحاب الإمام علي عليه السلام، مبني بـ React و React Router مع دعم كامل للغة العربية (RTL).

## المميزات

- 🌐 **صفحات قابلة للمشاركة**: كل صحابي له صفحة مخصصة برابط فريد
- 🔍 **بحث متقدم**: إمكانية البحث في أسماء الصحابة والأوصاف
- 📱 **تصميم متجاوب**: يعمل بشكل مثالي على جميع الأجهزة
- 🎨 **واجهة عصرية**: تصميم نظيف باستخدام Tailwind CSS
- ↔️ **دعم RTL**: مُحسّن للغة العربية
- 🔗 **مشاركة اجتماعية**: إمكانية مشاركة صفحات الصحابة
- 🖨️ **طباعة وحفظ**: إمكانية طباعة المحتوى أو حفظه كـ PDF

## التقنيات المستخدمة

- **React 18**: مكتبة واجهة المستخدم
- **React Router 6**: للتوجيه والتنقل
- **Tailwind CSS**: للتصميم والتنسيق
- **Vite**: أداة البناء والتطوير
- **Google Fonts**: خط Tajawal للعربية

## التثبيت والتشغيل

### المتطلبات
- Node.js (الإصدار 16 أو أحدث)
- npm أو yarn

### خطوات التثبيت

1. **استنساخ المشروع**
   ```bash
   git clone [repository-url]
   cd imam-ali-companions
   ```

2. **تثبيت التبعيات**
   ```bash
   npm install
   # أو
   yarn install
   ```

3. **تشغيل المشروع في وضع التطوير**
   ```bash
   npm run dev
   # أو
   yarn dev
   ```

4. **فتح المتصفح**
   سيتم فتح الموقع تلقائياً على `http://localhost:3000`

### البناء للإنتاج

```bash
npm run build
# أو
yarn build
```

سيتم إنشاء ملفات الإنتاج في مجلد `dist/`

### معاينة البناء

```bash
npm run preview
# أو
yarn preview
```

## هيكل المشروع

```
src/
├── components/
│   ├── Layout.jsx          # التخطيط العام للموقع
│   ├── HomePage.jsx        # الصفحة الرئيسية
│   └── CompanionDetail.jsx # صفحة تفاصيل الصحابي
├── data/
│   └── companionsData.js   # بيانات الصحابة
├── App.jsx                 # المكون الرئيسي
└── main.jsx               # نقطة دخول التطبيق
```

## الاستخدام

### الصفحة الرئيسية
- عرض جميع الصحابة في شبكة منظمة
- إمكانية البحث بالاسم أو الوصف
- أزرار للانتقال لصفحة التفاصيل أو المشاركة

### صفحة تفاصيل الصحابي
- معلومات مفصلة عن الصحابي
- المراجع والمصادر
- ملاحظات تعليمية
- أزرار للمشاركة والطباعة
- التنقل للصحابي السابق/التالي

### الروابط القابلة للمشاركة
كل صحابي له رابط فريد:
```
/companion/1  # مالك الأشتر النخعي
/companion/2  # عمار بن ياسر
...
```

## إضافة صحابة جدد

لإضافة صحابي جديد، قم بتعديل ملف `src/data/companionsData.js`:

```javascript
{
  id: 11, // رقم فريد
  name: "اسم الصحابي",
  years: "تاريخ الوفاة",
  short: "وصف مختصر",
  details: "تفاصيل مطولة",
  sources: ["مصدر 1", "مصدر 2"],
  slug: "companion-slug" // اختياري للروابط الودية
}
```

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى فتح Issue في المستودع.
