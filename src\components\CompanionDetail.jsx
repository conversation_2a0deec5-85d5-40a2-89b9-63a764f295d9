import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { getCompanionById, companionsData } from '../data/companionsData';

const CompanionDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [companion, setCompanion] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const foundCompanion = getCompanionById(id);
    if (foundCompanion) {
      setCompanion(foundCompanion);
      // تحديث عنوان الصفحة و meta tags
      document.title = `${foundCompanion.name} - صحابة الإمام علي`;

      // تحديث meta description
      const metaDescription = document.querySelector('meta[name="description"]');
      if (metaDescription) {
        metaDescription.setAttribute('content', foundCompanion.short);
      }

      // تحديث Open Graph tags
      const ogTitle = document.querySelector('meta[property="og:title"]');
      if (ogTitle) {
        ogTitle.setAttribute('content', `${foundCompanion.name} - صحابة الإمام علي`);
      }

      const ogDescription = document.querySelector('meta[property="og:description"]');
      if (ogDescription) {
        ogDescription.setAttribute('content', foundCompanion.short);
      }

      const ogUrl = document.querySelector('meta[property="og:url"]');
      if (ogUrl) {
        ogUrl.setAttribute('content', window.location.href);
      }
    } else {
      // إذا لم يتم العثور على الصحابي، توجيه للصفحة الرئيسية
      navigate('/');
    }
    setLoading(false);
  }, [id, navigate]);

  const handleShare = () => {
    const url = window.location.href;
    if (navigator.share) {
      navigator.share({
        title: `${companion.name} - صحابة الإمام علي`,
        text: companion.short,
        url: url,
      }).catch(console.error);
    } else if (navigator.clipboard) {
      navigator.clipboard.writeText(url).then(() => {
        alert('تم نسخ رابط الصفحة إلى الحافظة');
      }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = url;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('تم نسخ رابط الصفحة إلى الحافظة');
      });
    }
  };

  const getNextCompanion = () => {
    const currentIndex = companionsData.findIndex(c => c.id === companion.id);
    return currentIndex < companionsData.length - 1 ? companionsData[currentIndex + 1] : null;
  };

  const getPreviousCompanion = () => {
    const currentIndex = companionsData.findIndex(c => c.id === companion.id);
    return currentIndex > 0 ? companionsData[currentIndex - 1] : null;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-lg text-gray-600">جاري التحميل...</div>
      </div>
    );
  }

  if (!companion) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">الصحابي غير موجود</h2>
        <p className="text-gray-600 mb-6">عذراً، لم يتم العثور على الصحابي المطلوب.</p>
        <Link
          to="/"
          className="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
        >
          العودة للصفحة الرئيسية
        </Link>
      </div>
    );
  }

  const nextCompanion = getNextCompanion();
  const previousCompanion = getPreviousCompanion();

  return (
    <div className="max-w-4xl mx-auto">
      {/* معلومات الصحابي الرئيسية */}
      <div className="bg-white rounded-2xl shadow-sm p-8 mb-6">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{companion.name}</h1>
            <p className="text-lg text-gray-600">{companion.years}</p>
          </div>
          <div className="flex gap-3">
            <button
              onClick={handleShare}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              مشاركة الصفحة
            </button>
            <button
              onClick={() => window.print()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              طباعة
            </button>
          </div>
        </div>

        {/* الملخص */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-3 text-gray-800">نبذة مختصرة</h2>
          <p className="text-gray-700 leading-relaxed bg-gray-50 p-4 rounded-lg">
            {companion.short}
          </p>
        </div>

        {/* التفاصيل */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-3 text-gray-800">التفاصيل</h2>
          <div className="text-gray-700 leading-relaxed">
            <p>{companion.details}</p>
          </div>
        </div>

        {/* المراجع والمصادر */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3 text-gray-800">المراجع والمصادر</h3>
          <ul className="list-disc mr-6 text-gray-600 space-y-1">
            {companion.sources && companion.sources.map((source, index) => (
              <li key={index}>{source}</li>
            ))}
          </ul>
        </div>

        {/* ملاحظات تعليمية */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3 text-gray-800">ملاحظات تعليمية</h3>
          <ul className="list-disc mr-6 text-gray-600 space-y-2">
            <li>راجع مصادر السيرة والكتب المتخصصة للتوسع في شخصية {companion.name}.</li>
            <li>قارن بين دور {companion.name} والصحابة الآخرين في الجوانب العسكرية والسياسية والروحية.</li>
            <li>استخرج دروسًا أخلاقية وسلوكية من سيرة {companion.name}.</li>
            <li>ادرس السياق التاريخي الذي عاش فيه {companion.name} لفهم أفضل لدوره.</li>
          </ul>
        </div>
      </div>

      {/* التنقل بين الصحابة */}
      <div className="bg-white rounded-2xl shadow-sm p-6 mb-6">
        <h3 className="text-lg font-semibold mb-4 text-gray-800">تصفح المزيد</h3>
        <div className="flex justify-between items-center">
          <div className="flex-1">
            {previousCompanion && (
              <Link
                to={`/companion/${previousCompanion.id}`}
                className="block p-4 border rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="text-sm text-gray-500 mb-1">← السابق</div>
                <div className="font-medium text-gray-800">{previousCompanion.name}</div>
              </Link>
            )}
          </div>
          
          <div className="mx-4">
            <Link
              to="/"
              className="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
            >
              جميع الصحابة
            </Link>
          </div>

          <div className="flex-1 text-left">
            {nextCompanion && (
              <Link
                to={`/companion/${nextCompanion.id}`}
                className="block p-4 border rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="text-sm text-gray-500 mb-1">التالي →</div>
                <div className="font-medium text-gray-800">{nextCompanion.name}</div>
              </Link>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompanionDetail;
